{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <span class="layui-icon layui-icon-warning" style="color: #FF5722;"></span>
            <span style="color: #FF5722;">特别提示</span>
        </div>
        <div class="layui-card-body" style="background-color: #FFF7E6; border-left: 4px solid #FF5722;">
            <p style="margin: 0; color: #666;">
                状态记录:表示该工序是否完成数量记录:按产品规格上报该工序完成数量
            </p>
        </div>
    </div>

    <form class="layui-form" lay-filter="processTemplateForm">
        <input type="hidden" name="id" value="{$info.id|default=0}" />
        <input type="hidden" id="stepsData" value="{$info.steps_json|default='[]'}" />
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">* 工艺名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" value="{$info.name|default=''}" placeholder="请输入工艺名称" class="layui-input" lay-verify="required" maxlength="30" />
                        <div class="layui-word-aux">0 / 30</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <input type="text" name="remark" value="{$info.remark|default=''}" placeholder="请输入备注信息" class="layui-input" maxlength="20" />
                        <div class="layui-word-aux">0 / 20</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">工艺步骤</label>
            <div class="layui-input-block">
                <div class="process-steps-container">
                    <table class="layui-table" lay-skin="line">
                        <thead>
                            <tr>
                                <th width="60">步骤</th>
                                <th width="150">工序名称</th>
                                <th width="120">工序类型</th>
                                <th width="120">完成所需时间 <i class="layui-icon layui-icon-help" title="完成所需时间说明"></i></th>
                                <th width="100">加工类型</th>
                                <th width="100">检验方式</th>
                                <th width="200">工序描述</th>
                                <th width="80">操作</th>
                            </tr>
                        </thead>
                        <tbody id="processStepsTable">
                            <!-- 工艺步骤行将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                    <div class="layui-form-item" style="text-align: center; margin-top: 15px;">
                        <button type="button" class="layui-btn layui-btn-primary" id="addStepBtn">
                            <i class="layui-icon layui-icon-add-1"></i> 新增一行
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="processTemplateSubmit">保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'form'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;
        
        // 从后端获取工序列表
        var processList = {$processList|json_encode|raw};
        
        // 工序类型选项
        var processTypes = [
            {value: '数据记录', text: '数据记录'},
            {value: '状态记录', text: '状态记录'}
        ];
        
        // 加工类型选项
        var processingTypes = [
            {value: '自制', text: '自制'},
            {value: '外协', text: '外协'}
        ];
        
        // 检验方式选项
        var inspectionMethods = [
            {value: '免检', text: '免检'},
            {value: '抽检', text: '抽检'},
            {value: '全检', text: '全检'}
        ];
        
        // 初始化工艺步骤数据
        var processSteps = [];
        
        // 从隐藏字段获取步骤数据
        var stepsDataElement = document.getElementById('stepsData');
        if (stepsDataElement && stepsDataElement.value) {
            try {
                processSteps = JSON.parse(stepsDataElement.value);
            } catch(e) {
                processSteps = [];
            }
        }
        
        // 如果没有步骤，添加一个默认步骤
        if (processSteps.length === 0) {
            processSteps.push({
                step: 1,
                name: '',
                type: '数据记录',
                completion_time: 0,
                time_unit: '天',
                processing_type: '自制',
                inspection_method: '免检',
                description: ''
            });
        }
        
        // 渲染工艺步骤表格
        function renderProcessSteps() {
            var html = '';
            for (var i = 0; i < processSteps.length; i++) {
                var step = processSteps[i];
                html += '<tr data-index="' + i + '">';
                html += '<td>' + (i + 1) + '</td>';
                html += '<td><select name="steps[' + i + '][process_id]" lay-filter="processName" data-index="' + i + '">';
                html += '<option value="">请选择</option>';
                // 动态加载工序选项
                for (var p = 0; p < processList.length; p++) {
                    var selected = (step.process_id == processList[p].id) ? ' selected' : '';
                    html += '<option value="' + processList[p].id + '"' + selected + ' data-name="' + processList[p].name + '">' + processList[p].name + '</option>';
                }
                // 添加固定的刷新和新增选项
                html += '<option value="refresh" style="color: #1E9FFF; border-top: 1px solid #e6e6e6; margin-top: 5px; padding-top: 5px;">🔄 刷新</option>';
                html += '<option value="add_new" style="color: #FF5722;">➕ 新增工艺</option>';
                html += '</select>';
                // 添加隐藏字段存储工序名称，用于保存
                html += '<input type="hidden" name="steps[' + i + '][name]" value="' + (step.name || '') + '" />';
                html += '</td>';
                
                html += '<td><select name="steps[' + i + '][type]">';
                for (var j = 0; j < processTypes.length; j++) {
                    html += '<option value="' + processTypes[j].value + '"' + (step.type === processTypes[j].value ? ' selected' : '') + '>' + processTypes[j].text + '</option>';
                }
                html += '</select></td>';
                
                html += '<td>';
                html += '<div class="layui-input-inline" style="width: 60px;">';
                html += '<input type="number" name="steps[' + i + '][completion_time]" value="' + (step.completion_time || 0) + '" placeholder="请输入" class="layui-input" min="0" />';
                html += '</div>';
                html += '<div class="layui-input-inline" style="width: 50px;">';
                html += '<select name="steps[' + i + '][time_unit]">';
                html += '<option value="天"' + (step.time_unit === '天' ? ' selected' : '') + '>天</option>';
                html += '<option value="小时"' + (step.time_unit === '小时' ? ' selected' : '') + '>小时</option>';
                html += '</select>';
                html += '</div>';
                html += '</td>';
                
                html += '<td><select name="steps[' + i + '][processing_type]">';
                for (var k = 0; k < processingTypes.length; k++) {
                    html += '<option value="' + processingTypes[k].value + '"' + (step.processing_type === processingTypes[k].value ? ' selected' : '') + '>' + processingTypes[k].text + '</option>';
                }
                html += '</select></td>';
                
                html += '<td><select name="steps[' + i + '][inspection_method]">';
                for (var l = 0; l < inspectionMethods.length; l++) {
                    html += '<option value="' + inspectionMethods[l].value + '"' + (step.inspection_method === inspectionMethods[l].value ? ' selected' : '') + '>' + inspectionMethods[l].text + '</option>';
                }
                html += '</select></td>';
                
                html += '<td><input type="text" name="steps[' + i + '][description]" value="' + (step.description || '') + '" placeholder="请输入内容" class="layui-input" /></td>';
                
                html += '<td>';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary move-up" data-index="' + i + '" title="上移"><i class="layui-icon layui-icon-up"></i></button> ';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary move-down" data-index="' + i + '" title="下移"><i class="layui-icon layui-icon-down"></i></button>';
                html += '</td>';
                html += '</tr>';
            }
            $('#processStepsTable').html(html);
            form.render();
        }
        
        // 初始化渲染
        renderProcessSteps();
        
        // 添加新步骤
        $('#addStepBtn').click(function() {
            processSteps.push({
                step: processSteps.length + 1,
                name: '',
                type: '数据记录',
                completion_time: 0,
                time_unit: '天',
                processing_type: '自制',
                inspection_method: '免检',
                description: ''
            });
            renderProcessSteps();
        });
        
        // 上移步骤
        $(document).on('click', '.move-up', function() {
            var index = parseInt($(this).data('index'));
            if (index > 0) {
                var temp = processSteps[index];
                processSteps[index] = processSteps[index - 1];
                processSteps[index - 1] = temp;
                renderProcessSteps();
            }
        });
        
        // 下移步骤
        $(document).on('click', '.move-down', function() {
            var index = parseInt($(this).data('index'));
            if (index < processSteps.length - 1) {
                var temp = processSteps[index];
                processSteps[index] = processSteps[index + 1];
                processSteps[index + 1] = temp;
                renderProcessSteps();
            }
        });
        
        // 刷新工序列表
        function refreshProcessList() {
            layer.load(2);
            tool.get('/Produce/processTemplate/getProcessList', {}, function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    processList = res.data;
                    renderProcessSteps();
                    layer.msg('工序列表已刷新');
                } else {
                    layer.msg('刷新失败：' + res.msg);
                }
            });
        }
        
        // 新增工艺
        function addNewProcess() {
            // 打开新增工艺页面
            tool.side('/Produce/process/add', '新增工艺', '800px', '600px');
        }
        
        // 监听工序选择变化
        form.on('select(processName)', function(data) {
            var $option = $(data.elem).find('option:selected');
            var index = $(data.elem).data('index');
            var $row = $('#processStepsTable tr').eq(index);
            
            if (data.value === 'refresh') {
                // 刷新工序列表
                refreshProcessList();
                // 重置选择框
                $(data.elem).val('');
                form.render('select');
                return;
            } else if (data.value === 'add_new') {
                // 新增工艺
                addNewProcess();
                // 重置选择框
                $(data.elem).val('');
                form.render('select');
                return;
            } else if (data.value) {
                // 获取工序信息
                var processName = $option.data('name');
                
                // 更新隐藏的工序名称字段
                $row.find('input[name*="[name]"]').val(processName);
            } else {
                // 清空相关字段
                $row.find('input[name*="[name]"]').val('');
            }
        });
        
        // 字符计数
        $('input[name="name"]').on('input', function() {
            var len = $(this).val().length;
            $(this).siblings('.layui-word-aux').text(len + ' / 30');
        });
        
        $('input[name="remark"]').on('input', function() {
            var len = $(this).val().length;
            $(this).siblings('.layui-word-aux').text(len + ' / 20');
        });
        
        // 表单提交
        form.on('submit(processTemplateSubmit)', function(data) {
            // 收集工艺步骤数据
            var steps = [];
            $('#processStepsTable tr').each(function(index) {
                var $row = $(this);
                var processId = $row.find('select[name*="[process_id]"]').val();
                var processName = $row.find('input[name*="[name]"]').val();
                
                if (processId && processName) {
                    var step = {
                        step: index + 1,
                        process_id: parseInt(processId),
                        name: processName,
                        type: $row.find('select[name*="[type]"]').val(),
                        completion_time: parseInt($row.find('input[name*="[completion_time]"]').val()) || 0,
                        time_unit: $row.find('select[name*="[time_unit]"]').val(),
                        processing_type: $row.find('select[name*="[processing_type]"]').val(),
                        inspection_method: $row.find('select[name*="[inspection_method]"]').val(),
                        description: $row.find('input[name*="[description]"]').val()
                    };
                    steps.push(step);
                }
            });
            
            if (steps.length === 0) {
                layer.msg('请至少添加一个工艺步骤');
                return false;
            }
            
            // 添加步骤数据到表单
            data.field.steps = steps;
            
            let callback = function (e) {
                layer.msg(e.msg);
                if (e.code == 0) {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            }
            tool.post("/Produce/processTemplate/save", data.field, callback);
            return false;
        });
    }
</script>
{/block}

{block name="style"}
<style>
.process-steps-container {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
}

.process-steps-container .layui-table {
    margin-bottom: 0;
}

.process-steps-container .layui-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.layui-input-inline {
    display: inline-block;
    vertical-align: middle;
}

.move-up, .move-down {
    margin: 0 2px;
}

.layui-word-aux {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* 工序选择下拉框样式 */
.process-select-option-divider {
    border-top: 1px solid #e6e6e6;
    margin-top: 5px;
    padding-top: 5px;
}

.process-select-refresh {
    color: #1E9FFF !important;
    font-weight: bold;
}

.process-select-add {
    color: #FF5722 !important;
    font-weight: bold;
}

/* 工序选择框的特殊样式 */
select[name*="process_id"] option[value="refresh"],
select[name*="process_id"] option[value="add_new"] {
    background-color: #f8f9fa;
    font-weight: bold;
}

select[name*="process_id"] option[value="refresh"] {
    color: #1E9FFF;
    border-top: 1px solid #e6e6e6;
}

select[name*="process_id"] option[value="add_new"] {
    color: #FF5722;
}
</style>
{/block}