{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <span class="layui-icon layui-icon-warning" style="color: #FF5722;"></span>
            <span style="color: #FF5722;">特别提示</span>
        </div>
        <div class="layui-card-body" style="background-color: #FFF7E6; border-left: 4px solid #FF5722;">
            <p style="margin: 0; color: #666;">
                状态记录表示工序是否完成，<span style="color: #1E9FFF;">数据记录</span>一品部将上报数据工序成效量
            </p>
        </div>
    </div>

    <form class="layui-form" lay-filter="processTemplateForm">
        <input type="hidden" name="id" value="{$info.id|default=0}" />
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">* 工艺名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" value="{$info.name|default=''}" placeholder="请输入工艺名称" class="layui-input" lay-verify="required" maxlength="30" />
                        <div class="layui-word-aux">0 / 30</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <input type="text" name="remark" value="{$info.remark|default=''}" placeholder="请输入备注信息" class="layui-input" maxlength="20" />
                        <div class="layui-word-aux">0 / 20</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">工艺步骤</label>
            <div class="layui-input-block">
                <div class="process-steps-container">
                    <table class="layui-table" lay-skin="line">
                        <thead>
                            <tr>
                                <th width="60">步骤</th>
                                <th width="150">工序名称</th>
                                <th width="120">工序类型</th>
                                <th width="120">完成所需时间 <i class="layui-icon layui-icon-help" title="完成所需时间说明"></i></th>
                                <th width="100">加工类型</th>
                                <th width="100">检验方式</th>
                                <th width="200">工序描述</th>
                                <th width="80">操作</th>
                            </tr>
                        </thead>
                        <tbody id="processStepsTable">
                            <!-- 工艺步骤行将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                    <div class="layui-form-item" style="text-align: center; margin-top: 15px;">
                        <button type="button" class="layui-btn layui-btn-primary" id="addStepBtn">
                            <i class="layui-icon layui-icon-add-1"></i> 新增一行
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="processTemplateSubmit">保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'form'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;
        
        // 工序类型选项
        var processTypes = [
            {value: '数据记录', text: '数据记录'},
            {value: '状态记录', text: '状态记录'}
        ];
        
        // 加工类型选项
        var processingTypes = [
            {value: '自制', text: '自制'},
            {value: '外协', text: '外协'}
        ];
        
        // 检验方式选项
        var inspectionMethods = [
            {value: '免检', text: '免检'},
            {value: '抽检', text: '抽检'},
            {value: '全检', text: '全检'}
        ];
        
        // 初始化工艺步骤数据
        var processSteps = [];
        {php}
        if (isset($info['steps']) && !empty($info['steps'])) {
            $stepsJson = json_encode(json_decode($info['steps'], true) ?: []);
            echo "processSteps = " . $stepsJson . ";";
        }
        {/php}
        
        // 如果没有步骤，添加一个默认步骤
        if (processSteps.length === 0) {
            processSteps.push({
                step: 1,
                name: '',
                type: '数据记录',
                completion_time: 0,
                time_unit: '天',
                processing_type: '自制',
                inspection_method: '免检',
                description: ''
            });
        }
        
        // 渲染工艺步骤表格
        function renderProcessSteps() {
            var html = '';
            for (var i = 0; i < processSteps.length; i++) {
                var step = processSteps[i];
                html += '<tr data-index="' + i + '">';
                html += '<td>' + (i + 1) + '</td>';
                html += '<td><select name="steps[' + i + '][name]" lay-filter="processName" data-index="' + i + '">';
                html += '<option value="">请选择</option>';
                // 这里可以添加预设的工序名称选项
                html += '<option value="下料"' + (step.name === '下料' ? ' selected' : '') + '>下料</option>';
                html += '<option value="CNC"' + (step.name === 'CNC' ? ' selected' : '') + '>CNC</option>';
                html += '<option value="车床"' + (step.name === '车床' ? ' selected' : '') + '>车床</option>';
                html += '<option value="火花机"' + (step.name === '火花机' ? ' selected' : '') + '>火花机</option>';
                html += '<option value="表面处理"' + (step.name === '表面处理' ? ' selected' : '') + '>表面处理</option>';
                html += '<option value="质检"' + (step.name === '质检' ? ' selected' : '') + '>质检</option>';
                html += '<option value="粗车"' + (step.name === '粗车' ? ' selected' : '') + '>粗车</option>';
                html += '<option value="清洗研磨"' + (step.name === '清洗研磨' ? ' selected' : '') + '>清洗研磨</option>';
                html += '<option value="电镀"' + (step.name === '电镀' ? ' selected' : '') + '>电镀</option>';
                html += '<option value="全检"' + (step.name === '全检' ? ' selected' : '') + '>全检</option>';
                html += '</select></td>';
                
                html += '<td><select name="steps[' + i + '][type]">';
                for (var j = 0; j < processTypes.length; j++) {
                    html += '<option value="' + processTypes[j].value + '"' + (step.type === processTypes[j].value ? ' selected' : '') + '>' + processTypes[j].text + '</option>';
                }
                html += '</select></td>';
                
                html += '<td>';
                html += '<div class="layui-input-inline" style="width: 60px;">';
                html += '<input type="number" name="steps[' + i + '][completion_time]" value="' + (step.completion_time || 0) + '" placeholder="请输入" class="layui-input" min="0" />';
                html += '</div>';
                html += '<div class="layui-input-inline" style="width: 50px;">';
                html += '<select name="steps[' + i + '][time_unit]">';
                html += '<option value="天"' + (step.time_unit === '天' ? ' selected' : '') + '>天</option>';
                html += '<option value="小时"' + (step.time_unit === '小时' ? ' selected' : '') + '>小时</option>';
                html += '</select>';
                html += '</div>';
                html += '</td>';
                
                html += '<td><select name="steps[' + i + '][processing_type]">';
                for (var k = 0; k < processingTypes.length; k++) {
                    html += '<option value="' + processingTypes[k].value + '"' + (step.processing_type === processingTypes[k].value ? ' selected' : '') + '>' + processingTypes[k].text + '</option>';
                }
                html += '</select></td>';
                
                html += '<td><select name="steps[' + i + '][inspection_method]">';
                for (var l = 0; l < inspectionMethods.length; l++) {
                    html += '<option value="' + inspectionMethods[l].value + '"' + (step.inspection_method === inspectionMethods[l].value ? ' selected' : '') + '>' + inspectionMethods[l].text + '</option>';
                }
                html += '</select></td>';
                
                html += '<td><input type="text" name="steps[' + i + '][description]" value="' + (step.description || '') + '" placeholder="请输入内容" class="layui-input" /></td>';
                
                html += '<td>';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary move-up" data-index="' + i + '" title="上移"><i class="layui-icon layui-icon-up"></i></button> ';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary move-down" data-index="' + i + '" title="下移"><i class="layui-icon layui-icon-down"></i></button>';
                html += '</td>';
                html += '</tr>';
            }
            $('#processStepsTable').html(html);
            form.render();
        }
        
        // 初始化渲染
        renderProcessSteps();
        
        // 添加新步骤
        $('#addStepBtn').click(function() {
            processSteps.push({
                step: processSteps.length + 1,
                name: '',
                type: '数据记录',
                completion_time: 0,
                time_unit: '天',
                processing_type: '自制',
                inspection_method: '免检',
                description: ''
            });
            renderProcessSteps();
        });
        
        // 上移步骤
        $(document).on('click', '.move-up', function() {
            var index = parseInt($(this).data('index'));
            if (index > 0) {
                var temp = processSteps[index];
                processSteps[index] = processSteps[index - 1];
                processSteps[index - 1] = temp;
                renderProcessSteps();
            }
        });
        
        // 下移步骤
        $(document).on('click', '.move-down', function() {
            var index = parseInt($(this).data('index'));
            if (index < processSteps.length - 1) {
                var temp = processSteps[index];
                processSteps[index] = processSteps[index + 1];
                processSteps[index + 1] = temp;
                renderProcessSteps();
            }
        });
        
        // 字符计数
        $('input[name="name"]').on('input', function() {
            var len = $(this).val().length;
            $(this).siblings('.layui-word-aux').text(len + ' / 30');
        });
        
        $('input[name="remark"]').on('input', function() {
            var len = $(this).val().length;
            $(this).siblings('.layui-word-aux').text(len + ' / 20');
        });
        
        // 表单提交
        form.on('submit(processTemplateSubmit)', function(data) {
            // 收集工艺步骤数据
            var steps = [];
            $('#processStepsTable tr').each(function(index) {
                var $row = $(this);
                var step = {
                    step: index + 1,
                    name: $row.find('select[name*="[name]"]').val(),
                    type: $row.find('select[name*="[type]"]').val(),
                    completion_time: parseInt($row.find('input[name*="[completion_time]"]').val()) || 0,
                    time_unit: $row.find('select[name*="[time_unit]"]').val(),
                    processing_type: $row.find('select[name*="[processing_type]"]').val(),
                    inspection_method: $row.find('select[name*="[inspection_method]"]').val(),
                    description: $row.find('input[name*="[description]"]').val()
                };
                
                if (step.name) { // 只添加有工序名称的步骤
                    steps.push(step);
                }
            });
            
            if (steps.length === 0) {
                layer.msg('请至少添加一个工艺步骤');
                return false;
            }
            
            // 添加步骤数据到表单
            data.field.steps = steps;
            
            let callback = function (e) {
                layer.msg(e.msg);
                if (e.code == 0) {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            }
            tool.post("/Produce/processTemplate/save", data.field, callback);
            return false;
        });
    }
</script>
{/block}

{block name="style"}
<style>
.process-steps-container {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
}

.process-steps-container .layui-table {
    margin-bottom: 0;
}

.process-steps-container .layui-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.layui-input-inline {
    display: inline-block;
    vertical-align: middle;
}

.move-up, .move-down {
    margin: 0 2px;
}

.layui-word-aux {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}
</style>
{/block}