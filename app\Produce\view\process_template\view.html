{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>工艺路线详情</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工艺编号：</label>
                        <div class="layui-input-block">
                            <span class="layui-badge layui-bg-blue">{$info.template_no|default=''}</span>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工艺名称：</label>
                        <div class="layui-input-block">
                            <span>{$info.name|default=''}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">备注：</label>
                <div class="layui-input-block">
                    <span>{$info.remark|default='无'}</span>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">创建时间：</label>
                <div class="layui-input-block">
                    <span>{$info.create_time_format|default=''}</span>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">更新时间：</label>
                <div class="layui-input-block">
                    <span>{$info.update_time_format|default=''}</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>工艺步骤流程</h3>
        </div>
        <div class="layui-card-body">
            {if condition="isset($info['steps_data']) && !empty($info['steps_data'])"}
            <div class="process-flow-visual">
                {volist name="info.steps_data" id="step" key="k"}
                <div class="process-step-item">
                    <div class="step-number">{$k}</div>
                    <div class="step-content">
                        <div class="step-name">{$step.name|default=''}</div>
                        <div class="step-details">
                            <span class="step-type">{$step.type|default=''}</span>
                            <span class="step-time">{$step.completion_time|default=0}{$step.time_unit|default='天'}</span>
                            <span class="step-processing">{$step.processing_type|default=''}</span>
                            <span class="step-inspection">{$step.inspection_method|default=''}</span>
                        </div>
                        {if condition="!empty($step['description'])"}
                        <div class="step-description">{$step.description}</div>
                        {/if}
                    </div>
                    {if condition="$k < count($info['steps_data'])"}
                    <div class="step-arrow">→</div>
                    {/if}
                </div>
                {/volist}
            </div>
            {else}
            <div class="layui-empty">
                <div class="layui-empty-icon">
                    <i class="layui-icon layui-icon-template-1"></i>
                </div>
                <p class="layui-empty-text">暂无工艺步骤</p>
            </div>
            {/if}
        </div>
    </div>
    
    <div class="layui-form-item" style="text-align: center; margin-top: 20px;">
        <button type="button" class="layui-btn" onclick="editTemplate()">编辑工艺路线</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="closeWindow()">关闭</button>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 编辑工艺路线
        window.editTemplate = function() {
            tool.side("/Produce/processTemplate/add?id={$info.id|default=0}");
        }
        
        // 关闭窗口
        window.closeWindow = function() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    }
</script>
{/block}

{block name="style"}
<style>
.process-flow-visual {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.process-step-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.step-number {
    width: 30px;
    height: 30px;
    background-color: #1E9FFF;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.step-content {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 200px;
}

.step-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.step-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 5px;
}

.step-details span {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    background-color: #f0f0f0;
    color: #666;
}

.step-type {
    background-color: #e6f7ff !important;
    color: #1890ff !important;
}

.step-time {
    background-color: #fff7e6 !important;
    color: #fa8c16 !important;
}

.step-processing {
    background-color: #f6ffed !important;
    color: #52c41a !important;
}

.step-inspection {
    background-color: #fff1f0 !important;
    color: #ff4d4f !important;
}

.step-description {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.step-arrow {
    font-size: 20px;
    color: #1E9FFF;
    font-weight: bold;
}

.layui-empty {
    text-align: center;
    padding: 40px;
}

.layui-empty-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.layui-empty-text {
    color: #999;
    font-size: 14px;
}

@media (max-width: 768px) {
    .process-flow-visual {
        flex-direction: column;
    }
    
    .step-arrow {
        transform: rotate(90deg);
    }
}
</style>
{/block}