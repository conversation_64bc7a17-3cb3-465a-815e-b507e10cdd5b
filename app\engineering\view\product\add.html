{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="layui-tab layui-tab-brief" lay-filter="productTab">
    <ul class="layui-tab-title">
        <li class="layui-this">基础资料</li>
        <li>质检信息</li>
        <li>配置信息</li>
        <li>价格信息</li>
        <li>库存管理方式</li>
        <li>工艺管理</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 基础资料 -->
        <div class="layui-tab-item layui-show">
            <form class="layui-form p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">产品编号<font>*</font></td>
                        <td>
                            <input type="text" name="material_code" value="" lay-verify="required" lay-reqText="请输入产品编号" autocomplete="off" placeholder="请输入产品编号" class="layui-input" id="material_code_input">
                            <div style="margin-top: 5px;">
                                <input type="checkbox" name="use_system_code" id="use_system_code" title="系统自动编号" lay-skin="primary" lay-filter="use_system_code">
                                <label for="use_system_code" style="margin-left: 5px; font-size: 12px; color: #666;">系统自动编号</label>
                            </div>
                        </td>
                        <td class="layui-td-gray">产品名称<font>*</font></td>
                        <td><input type="text" name="title" value="" lay-verify="required" lay-reqText="请输入产品名称" autocomplete="off" placeholder="请输入产品名称" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">产品分类<font>*</font></td>
                        <td>
                            <select name="cate_id" lay-verify="required" lay-reqText="请选择产品分类">
                                <option value="">请选择产品分类</option>
                                {volist name=":set_recursion(get_base_data('ProductCate'))" id="v"}
                                <option value="{$v.id}">{$v.title}</option>
                                {/volist}
                            </select>
                        </td>
                        <td class="layui-td-gray">基本单位<font>*</font></td>
                        <td>
                            <select name="unit" lay-verify="required" lay-reqText="请选择基本单位">
                                <option value="">请选择基本单位</option>
                                <option value="个">个</option>
                                <option value="件">件</option>
                                <option value="套">套</option>
                                <option value="台">台</option>
                                <option value="米">米</option>
                                <option value="千克">千克</option>
                                <option value="吨">吨</option>
                                <option value="升">升</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">产品规格</td>
                        <td><input type="text" name="specs" value="" autocomplete="off" placeholder="请输入产品规格" class="layui-input"></td>
                        <td class="layui-td-gray">产品类型<font>*</font></td>
                        <td>
                            <input type="radio" name="source_type" value="1" title="自产" checked>
                            <input type="radio" name="source_type" value="2" title="外购">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">状态</td>
                        <td>
                            <input type="radio" name="status" value="1" title="启用" checked>
                            <input type="radio" name="status" value="0" title="禁用">
                        </td>
                        <td class="layui-td-gray"></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">产品描述</td>
                        <td colspan="3"><textarea name="description" placeholder="请输入产品描述" class="layui-textarea"></textarea></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">备注信息</td>
                        <td colspan="3"><textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">产品图片</td>
                        <td colspan="3">
                            <button type="button" class="layui-btn" id="uploadProductImages">
                                <i class="layui-icon">&#xe67c;</i>上传图片
                            </button>
                            <div class="layui-form-mid layui-word-aux">最多上传6张图片,支持JPG,PNG,BMP格式，最大不超过5M</div>
                            <div id="productImagesList" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">产品图纸</td>
                        <td colspan="3">
                            <button type="button" class="layui-btn" id="uploadProductDrawing">
                                <i class="layui-icon">&#xe67c;</i>上传图纸
                            </button>
                            <div class="layui-form-mid layui-word-aux">支持上传1张图片,支持JPG,PNG,BMP格式，最大不超过5M</div>
                            <div id="productDrawingList" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        
        <!-- 质检信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-4">
                <div class="layui-form-item">
                    <label class="layui-form-label">质检配置</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="quality_control" title="质检管理" lay-skin="switch" lay-text="开启|关闭" lay-filter="quality_control">
                    </div>
                </div>
                
                <div id="quality_options" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">免检设置</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="skip_all_inspection" value="1" lay-skin="switch" lay-text="开启|关闭" lay-filter="skip_all_inspection">
                        </div>
                    </div>
                    
                    <div id="inspection_options">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="checkbox" name="purchase_inspection" title="采购入库检" lay-skin="primary">
                                <input type="checkbox" name="external_inspection" title="外协入库检" lay-skin="primary">
                                <input type="checkbox" name="production_inspection" title="生产入库检" lay-skin="primary">
                                <input type="checkbox" name="sales_inspection" title="销售出库检" lay-skin="primary">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 配置信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-4">
                <div class="layui-row">
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">默认仓库</label>
                            <div class="layui-input-block">
                                <select name="default_warehouse">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">默认客户</label>
                            <div class="layui-input-block">
                                <select name="default_customer">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="customer_code" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row">
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">产品编号</label>
                            <div class="layui-input-block">
                                <select name="product_number">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">序列号管理</label>
                            <div class="layui-input-block">
                                <select name="serial_management">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row">
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">产品有效期(天)</label>
                            <div class="layui-input-block">
                                <input type="text" name="validity_period" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">最小起订量</label>
                            <div class="layui-input-block">
                                <input type="text" name="min_order_qty" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">最小包装量</label>
                            <div class="layui-input-block">
                                <input type="text" name="min_package_qty" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 价格信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-4">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">参考成本价</label>
                            <div class="layui-input-block">
                                <input type="text" name="reference_cost" placeholder="请输入基本单位成本价" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">销售单价（含税）</label>
                            <div class="layui-input-block">
                                <input type="text" name="sales_price_tax" placeholder="请输入含税销售价" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">最低销售单价</label>
                            <div class="layui-input-block">
                                <input type="text" name="min_sales_price_tax" placeholder="请输入最低销售价" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">最高销售单价</label>
                            <div class="layui-input-block">
                                <input type="text" name="max_sales_price_tax" placeholder="请输入最高销售价" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">价格回检</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="enable_price_check" title="启用价格回检">
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 库存管理方式 -->
        <div class="layui-tab-item">
            <form class="layui-form p-4">
                <div class="layui-form-item">
                    <label class="layui-form-label">库存管理方式</label>
                    <div class="layui-input-block">
                        <input type="radio" name="inventory_method" value="unified" title="统一管理库存" checked>
                        <input type="radio" name="inventory_method" value="separate" title="分仓管理库存">
                    </div>
                </div>
                
                <div class="layui-row">
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">最低库存数（基本单位）</label>
                            <div class="layui-input-block">
                                <input type="text" name="min_stock" placeholder="请输入" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">最高库存数（基本单位）</label>
                            <div class="layui-input-block">
                                <input type="text" name="max_stock" placeholder="请输入" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">安全库存数（基本单位）</label>
                            <div class="layui-input-block">
                                <input type="text" name="safety_stock" placeholder="请输入" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 工艺管理 -->
        <div class="layui-tab-item">
            <div class="layui-tab layui-tab-brief" lay-filter="processTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">工艺管理</li>
                    <li>BOM明细</li>
                    <li>产品单价</li>
                    <li>外协单价</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 工艺管理 -->
                    <div class="layui-tab-item layui-show">
                        <div class="p-4">
                            <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" id="selectProcessTemplate">选择工艺路线</button>
                            <table class="layui-table" lay-filter="processTable">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>工序名称</th>
                                        <th>单价</th>
                                        <th>对应不良项</th>
                                        <th>工序类型</th>
                                    </tr>
                                </thead>
                                <tbody id="processTableBody">
                                    <tr>
                                        <td colspan="5" style="text-align: center; color: #999;">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- BOM明细 -->
                    <div class="layui-tab-item">
                        <div class="p-4">
                            <div class="layui-form-item">
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="addBomRow">新增一行</button>
                                <button type="button" class="layui-btn layui-btn-warm layui-btn-sm" id="importBom">导入BOM</button>
                            </div>
                            
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th><span style="color: red;">*</span>物料编号</th>
                                        <th><span style="color: red;">*</span>物料名称</th>
                                        <th>规格型号</th>
                                        <th><span style="color: red;">*</span>用量</th>
                                        <th>单位</th>
                                        <th>损耗率(%)</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="bomTableBody">
                                    <tr>
                                        <td colspan="9" style="text-align: center; color: #999;">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 产品单价 -->
                    <div class="layui-tab-item">
                        <div class="p-4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">设定采购价格区间：</label>
                                <div class="layui-input-inline" style="width: 200px;">
                                    <input type="text" name="price_range" placeholder="请输入单价格区间" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">多阶价单价配置：</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="price_config" value="latest" title="按最新单价匹配" checked>
                                    <input type="radio" name="price_config" value="lowest" title="按最低单价匹配">
                                </div>
                            </div>
                            
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th><span style="color: red;">*</span>供应商名称</th>
                                        <th>供应商编号</th>
                                        <th><span style="color: red;">*</span>采购含税单价（基本单位）</th>
                                        <th>税率</th>
                                        <th><span style="color: red;">*</span>不含税单价（基本单位）</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="priceTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; color: #999;">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="addPriceRow">新增一行</button>
                            <button type="button" class="layui-btn layui-btn-warm layui-btn-sm" id="refreshSupplier">刷新供应商</button>
                        </div>
                    </div>
                    
                    <!-- 外协单价 -->
                    <div class="layui-tab-item">
                        <div class="p-4">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>供应商名称</th>
                                        <th>供应商编号</th>
                                        <th><span style="color: red;">*</span>外协含税单价（基本单位）</th>
                                        <th>税率</th>
                                        <th><span style="color: red;">*</span>外协不含税单价（基本单位）</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="outsourceTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; color: #999;">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="addOutsourceRow">新增一行</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="layui-form-item" style="margin-top: 20px;">
    <div class="layui-input-block">
        <input type="hidden" name="id" value="0"/>
        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="webform">立即提交</button>
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</div>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker','uploadPlus'];
function gouguInit() {
    layui.use(['form', 'upload', 'element', 'table'], function () {
        var form = layui.form, 
            upload = layui.upload, 
            element = layui.element,
            table = layui.table,
            tool = layui.tool;
        
        // 选择工艺路线
        $('#selectProcessTemplate').on('click', function() {
            tool.side('/Produce/processTemplate/select', '选择工艺路线', '800px', '600px', function(data) {
                if (data && data.processes) {
                    loadProcessList(data.processes);
                }
            });
        });
        
        // 导入BOM
        $('#importBom').on('click', function() {
            layer.msg('BOM导入功能开发中...');
        });
        
        // 添加产品单价行
        $('#addPriceRow').on('click', function() {
            var rowIndex = $('#priceTableBody tr').length;
            var html = '<tr>';
            html += '<td>' + generateSupplierSelect('supplier_id[]') + '</td>';
            html += '<td><input type="text" name="supplier_code[]" class="layui-input" readonly></td>';
            html += '<td><input type="text" name="price_with_tax[]" class="layui-input"></td>';
            html += '<td><input type="text" name="tax_rate[]" class="layui-input" value="13"></td>';
            html += '<td><input type="text" name="price_without_tax[]" class="layui-input" readonly></td>';
            html += '<td><button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-row">删除</button></td>';
            html += '</tr>';
            
            if ($('#priceTableBody tr').length === 1 && $('#priceTableBody tr').text().indexOf('暂无数据') > -1) {
                $('#priceTableBody').html(html);
            } else {
                $('#priceTableBody').append(html);
            }
            
            // 重新渲染表单
            form.render('select');
        });
        
        // 添加外协单价行
        $('#addOutsourceRow').on('click', function() {
            var html = '<tr>';
            html += '<td>' + generateSupplierSelect('outsource_supplier_id[]') + '</td>';
            html += '<td><input type="text" name="outsource_supplier_code[]" class="layui-input" readonly></td>';
            html += '<td><input type="text" name="outsource_price_with_tax[]" class="layui-input"></td>';
            html += '<td><input type="text" name="outsource_tax_rate[]" class="layui-input" value="13"></td>';
            html += '<td><input type="text" name="outsource_price_without_tax[]" class="layui-input" readonly></td>';
            html += '<td><button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-row">删除</button></td>';
            html += '</tr>';
            
            if ($('#outsourceTableBody tr').length === 1 && $('#outsourceTableBody tr').text().indexOf('暂无数据') > -1) {
                $('#outsourceTableBody').html(html);
            } else {
                $('#outsourceTableBody').append(html);
            }
            
            // 重新渲染表单
            form.render('select');
        });
        
        // 监听供应商选择变化
        $(document).on('change', '.supplier-select', function() {
            var $this = $(this);
            var selectedOption = $this.find('option:selected');
            var supplierCode = selectedOption.data('code') || '';
            
            // 自动填充供应商编号
            $this.closest('tr').find('input[name*="supplier_code"]').val(supplierCode);
        });
        
        // 监听含税单价和税率变化，自动计算不含税单价
        $(document).on('input', 'input[name*="price_with_tax"], input[name*="tax_rate"]', function() {
            var $row = $(this).closest('tr');
            var priceWithTax = parseFloat($row.find('input[name*="price_with_tax"]').val()) || 0;
            var taxRate = parseFloat($row.find('input[name*="tax_rate"]').val()) || 0;
            
            if (priceWithTax > 0 && taxRate >= 0) {
                var priceWithoutTax = priceWithTax / (1 + taxRate / 100);
                $row.find('input[name*="price_without_tax"]').val(priceWithoutTax.toFixed(4));
            }
        });
        
        // 删除行事件
        $(document).on('click', '.remove-row', function() {
            $(this).closest('tr').remove();
        });
        
        // 监听系统自动编号复选框
        form.on('checkbox(use_system_code)', function(data){
            var materialCodeInput = $('#material_code_input');
            if(data.elem.checked){
                // 勾选时禁用输入框并清空内容，设置提示文字
                materialCodeInput.prop('disabled', true).val('').attr('placeholder', '系统将自动生成编号');
                materialCodeInput.css('background-color', '#f5f5f5');
            } else {
                // 取消勾选时启用输入框
                materialCodeInput.prop('disabled', false).attr('placeholder', '请输入产品编号');
                materialCodeInput.css('background-color', '#fff');
            }
        });
        
        // 监听质检管理开关
        // 监听质检管理开关
        form.on('switch(quality_control)', function(data){
            console.log('质检配置开关状态:', data.elem.checked);
            if(data.elem.checked){
                // 开启质检管理时显示质检选项
                $('#quality_options').show();
                // 显示具体质检选项，默认全部选中（表示需要检验）
                $('#inspection_options').show();
                $('input[name$="_inspection"]').prop('checked', true);
                // 确保免检设置开关保持关闭状态
                $('input[name="skip_all_inspection"]').prop('checked', false);
                form.render('checkbox');
                form.render('switch');
            } else {
                // 关闭质检管理时隐藏所有质检选项
                $('#quality_options').hide();
                $('#inspection_options').hide();
                // 同时关闭免检设置和清除所有质检选项
                $('input[name="skip_all_inspection"]').prop('checked', false);
                $('input[name$="_inspection"]').prop('checked', false);
                form.render('checkbox');
                form.render('switch');
            }
        });
        
        // 监听免检设置开关
        form.on('switch(skip_all_inspection)', function(data){
            console.log('免检开关被点击，状态:', data.elem.checked);
            var isChecked = data.elem.checked;
            
            if(isChecked){
                // 免检开启时，自动取消所有入库检选项
                console.log('开启免检，取消所有检验项目');
                $('input[name="purchase_inspection"]').prop('checked', false);
                $('input[name="external_inspection"]').prop('checked', false);
                $('input[name="production_inspection"]').prop('checked', false);
                $('input[name="sales_inspection"]').prop('checked', false);
            } else {
                // 免检关闭时，自动选中所有入库检选项
                console.log('关闭免检，选中所有检验项目');
                $('input[name="purchase_inspection"]').prop('checked', true);
                $('input[name="external_inspection"]').prop('checked', true);
                $('input[name="production_inspection"]').prop('checked', true);
                $('input[name="sales_inspection"]').prop('checked', true);
            }
            
            // 重新渲染复选框
            form.render('checkbox');
        });
        
        // 产品图片上传
        var uploadProductImages = upload.render({
            elem: '#uploadProductImages',
            url: '{:url("adm/upload/file")}',
            multiple: true,
            accept: 'images',
            done: function (res) {
                if (res.code == 0) {
                    var html = '<div class="upload-item" style="display: inline-block; margin: 5px;">';
                    html += '<img src="' + res.data.file + '" style="width: 100px; height: 100px; object-fit: cover;">';
                    html += '<input type="hidden" name="product_images[]" value="' + res.data.file + '">';
                    html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-image" style="display: block; margin-top: 5px;">删除</button>';
                    html += '</div>';
                    $('#productImagesList').append(html);
                }
            }
        });
        
        // 产品图纸上传
        var uploadProductDrawing = upload.render({
            elem: '#uploadProductDrawing',
            url: '{:url("adm/upload/file")}',
            accept: 'images',
            done: function (res) {
                if (res.code == 0) {
                    var html = '<div class="upload-item" style="display: inline-block; margin: 5px;">';
                    html += '<img src="' + res.data.file + '" style="width: 100px; height: 100px; object-fit: cover;">';
                    html += '<input type="hidden" name="product_drawing" value="' + res.data.file + '">';
                    html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-image" style="display: block; margin-top: 5px;">删除</button>';
                    html += '</div>';
                    $('#productDrawingList').html(html); // 只允许一张图纸
                }
            }
        });
        
        // 删除图片事件
        $(document).on('click', '.remove-image', function() {
            $(this).closest('.upload-item').remove();
        });
        
        // 表单提交
        form.on('submit(webform)', function (data) {
            var index = layer.load(2);
            $.ajax({
                url: '{:url("engineering/product/add")}',
                data: data.field,
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    layer.close(index);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function () {
                            tool.sideClose(1000);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, anim: 6});
                    }
                }
            });
            return false;
        });
        
        // 刷新供应商按钮事件
        // 刷新供应商按钮事件
        $('#refreshSupplier').on('click', function() {
            layer.msg('正在刷新供应商数据...');
            loadSupplierList();
        });
        
        // 初始化加载供应商数据
        loadSupplierList();
        
        // 初始化加载仓库数据
        // 初始化加载仓库数据
        loadWarehouseList();
        
        // 初始化加载客户数据
        loadCustomerList();
        
        // 加载工序列表
        function loadProcessList(processes) {
            var html = '';
            layui.each(processes, function(index, item) {
                html += '<tr>';
                html += '<td>' + (index + 1) + '</td>';
                html += '<td>' + item.name + '</td>';
                html += '<td><input type="text" name="process_price_' + item.id + '" class="layui-input" style="width: 100px;"></td>';
                html += '<td>' + (item.defect_items || '') + '</td>';
                html += '<td>' + (item.type_name || '') + '</td>';
                html += '</tr>';
            });
            $('#processTableBody').html(html);
        }
        
        // 供应商数据
        var supplierList = [];
        
        // 加载供应商列表
        function loadSupplierList() {
            // 添加测试数据
            supplierList = [
                {id: 1, title: '浙江德清华夏科技有限公司', supplier_code: 'ZJDQ001'},
                {id: 2, title: '测试供应商', supplier_code: 'TEST001'}
            ];
        }
        
        // 生成供应商选择框HTML
        function generateSupplierSelect(name, selectedId) {
            var html = '<select name="' + name + '" class="layui-input supplier-select">';
            html += '<option value="">请选择供应商</option>';
            layui.each(supplierList, function(index, item) {
                var selected = selectedId == item.id ? 'selected' : '';
                html += '<option value="' + item.id + '" data-code="' + item.supplier_code + '" ' + selected + '>' + item.title + '</option>';
            });
            html += '</select>';
            return html;
        }
        
        // 添加BOM明细行
        // 添加BOM明细行
        $('#addBomRow').on('click', function() {
            var rowIndex = $('#bomTableBody tr').length;
            var html = '<tr>';
            html += '<td>' + (rowIndex === 1 && $('#bomTableBody tr').text().indexOf('暂无数据') > -1 ? 1 : rowIndex) + '</td>';
            html += '<td><input type="text" name="material_code[]" class="layui-input" placeholder="请输入物料编号"></td>';
            html += '<td><input type="text" name="material_name[]" class="layui-input" placeholder="请输入物料名称"></td>';
            html += '<td><input type="text" name="material_spec[]" class="layui-input" placeholder="请输入规格型号"></td>';
            html += '<td><input type="text" name="material_qty[]" class="layui-input" placeholder="请输入用量"></td>';
            html += '<td><input type="text" name="material_unit[]" class="layui-input" placeholder="请输入单位"></td>';
            html += '<td><input type="text" name="loss_rate[]" class="layui-input" placeholder="请输入损耗率"></td>';
            html += '<td><input type="text" name="bom_remark[]" class="layui-input" placeholder="请输入备注"></td>';
            html += '<td><button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-row">删除</button></td>';
            html += '</tr>';
            
            if ($('#bomTableBody tr').length === 1 && $('#bomTableBody tr').text().indexOf('暂无数据') > -1) {
                $('#bomTableBody').html(html);
            } else {
                $('#bomTableBody').append(html);
            }
        });
        
        // 仓库数据
        var warehouseList = [];
        
        // 加载仓库列表
        function loadWarehouseList() {
            tool.get('{:url("engineering/product/getWarehouseList")}', {}, function(res) {
               // console.log('仓库接口响应:', res);
                if (res.code === 0 && res.data) {
                    warehouseList = res.data;
                    // 填充默认仓库选择框
                    var warehouseSelect = $('select[name="default_warehouse"]');
                    warehouseSelect.empty().append('<option value="">请选择</option>');
                    
                    layui.each(warehouseList, function(index, item) {
                        var selected = item.is_default == 1 ? 'selected' : '';
                        warehouseSelect.append('<option value="' + item.id + '" ' + selected + '>' + item.title + '</option>');
                    });
                    
                    // 重新渲染选择框
                    form.render('select');
                   // layer.msg('成功加载 ' + res.data.length + ' 个仓库');
                  //  console.log('成功加载仓库数据:', warehouseList);
                } else {
                    layer.msg('加载仓库数据失败: ' + (res.msg || '未知错误'));
                    console.log('加载仓库数据失败:', res);
                }
            }, function(error) {
                console.log('仓库接口请求失败:', error);
                layer.msg('仓库接口请求失败，请检查网络连接');
                layer.msg('仓库接口请求失败，请检查网络连接');
            });
        }
        
        // 客户数据
        var customerList = [];
        
        // 加载客户列表
        function loadCustomerList() {
            tool.get('{:url("engineering/product/getCustomerList")}', {}, function(res) {
                //console.log('客户接口响应:', res);
                if (res.code === 0 && res.data) {
                    customerList = res.data;
                    // 填充默认客户选择框
                    var customerSelect = $('select[name="default_customer"]');
                    customerSelect.empty().append('<option value="">请选择</option>');
                    
                    layui.each(customerList, function(index, item) {
                        var selected = item.is_default == 1 ? 'selected' : '';
                        customerSelect.append('<option value="' + item.id + '" data-code="' + item.code + '" ' + selected + '>' + item.title + '</option>');
                    });
                    
                    // 重新渲染选择框
                    form.render('select');
                   // layer.msg('成功加载 ' + res.data.length + ' 个客户');
                   // console.log('成功加载客户数据:', customerList);
                } else {
                    layer.msg('加载客户数据失败: ' + (res.msg || '未知错误'));
                    console.log('加载客户数据失败:', res);
                }
            }, function(error) {
                console.log('客户接口请求失败:', error);
                layer.msg('客户接口请求失败，请检查网络连接');
            });
        }
        
        // 监听客户选择变化，自动填充客户编号
        $(document).on('change', 'select[name="default_customer"]', function() {
            var $this = $(this);
            var selectedOption = $this.find('option:selected');
            var customerCode = selectedOption.data('code') || '';
            
            // 自动填充客户编号
            $('input[name="customer_code"]').val(customerCode);
        });
    });
}
</script>
{/block}
