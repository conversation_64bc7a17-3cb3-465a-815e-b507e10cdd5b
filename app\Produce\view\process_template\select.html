{extend name="../../base/view/common/base" /}

 
{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.process-info {
    margin-top: 10px;
    padding: 10px;
    background: #f8f8f8;
    border-radius: 4px;
}
.process-list {
    margin-top: 5px;
}
.process-item {
    display: inline-block;
    margin: 2px 5px;
    padding: 2px 8px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 3px;
    font-size: 12px;
}
.btn-select {
    background: #1890ff;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 3px;
    cursor: pointer;
}
.btn-select:hover {
    background: #40a9ff;
}
</style>
{/block}

{block name="body"}
<div class="layui-fluid">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>选择工艺路线</h3>
                </div>
                <div class="layui-card-body">
                    <!-- 搜索区域 -->
                    <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="keywords" placeholder="工艺路线编码/名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button type="button" class="layui-btn" id="searchBtn">
                                    <i class="layui-icon layui-icon-search"></i> 搜索
                                </button>
                                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                                    <i class="layui-icon layui-icon-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <table class="layui-hide" id="processTemplateTable" lay-filter="processTemplateTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮模板 -->
<script type="text/html" id="operationTpl">
    <button class="btn-select" onclick="selectTemplate({{d.id}}, '{{d.name}}')">选择</button>
    <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="viewProcesses({{d.id}})">查看工序</button>
</script>
{/block}

{block name="script"}
<script>
const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker','uploadPlus'];
function gouguInit() {
    layui.use(['table', 'form', 'layer'], function(){
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        
        // 渲染表格
        var tableIns = table.render({
            elem: '#processTemplateTable',
            url: '{:url("processTemplate/select")}',
            method: 'post',
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 80, align: 'center'},
                {field: 'code', title: '工艺路线编码', width: 150, align: 'center'},
                {field: 'name', title: '工艺路线名称', width: 200},
                {field: 'remark', title: '备注', minWidth: 200},
                {field: 'create_time', title: '创建时间', width: 160, align: 'center', templet: function(d){
                    return d.create_time ? layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm') : '';
                }},
                {title: '操作', width: 180, align: 'center', toolbar: '#operationTpl'}
            ]],
            text: {
                none: '暂无工艺路线数据'
            }
        });
        
        // 搜索
        $('#searchBtn').on('click', function(){
            var keywords = $('input[name="keywords"]').val();
            tableIns.reload({
                where: {
                    keywords: keywords
                },
                page: {
                    curr: 1
                }
            });
        });
        
        // 重置
        $('#resetBtn').on('click', function(){
            $('input[name="keywords"]').val('');
            tableIns.reload({
                where: {},
                page: {
                    curr: 1
                }
            });
        });
        
        // 回车搜索
        $('input[name="keywords"]').on('keydown', function(e){
            if(e.keyCode === 13) {
                $('#searchBtn').click();
            }
        });
    });
}

// 选择工艺路线
function selectTemplate(templateId, templateName) {
    // 获取工艺路线详情
    $.post('{:url("processTemplate/getProcessDetail")}', {
        template_id: templateId
    }, function(res) {
        if (res.code == 0 && res.data) {
            var result = {
                template_id: templateId,
                template_name: templateName,
                processes: res.data.processes || []
            };
            
            // 调用父窗口的回调函数
            if (parent && parent.layui && parent.layui.layer) {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                
                // 如果父窗口有回调函数，则调用
                if (parent.processTemplateCallback && typeof parent.processTemplateCallback === 'function') {
                    parent.processTemplateCallback(result);
                }
            }
        } else {
            layer.msg(res.msg || '获取工艺路线详情失败', {icon: 2});
        }
    }).fail(function() {
        layer.msg('网络请求失败', {icon: 2});
    });
}

// 查看工序详情
function viewProcesses(templateId) {
    $.post('{:url("processTemplate/getProcessDetail")}', {
        template_id: templateId
    }, function(res) {
        if (res.code == 0 && res.data) {
            var processes = res.data.processes || [];
            var content = '';
            
            if (processes.length > 0) {
                content = '<div style="padding: 15px;">';
                content += '<h4 style="margin-bottom: 15px;">工序列表：</h4>';
                content += '<table class="layui-table" style="margin: 0;">';
                content += '<thead><tr><th>序号</th><th>工序名称</th><th>工序类型</th><th>缺陷项目</th></tr></thead>';
                content += '<tbody>';
                
                layui.each(processes, function(index, item) {
                    content += '<tr>';
                    content += '<td>' + (index + 1) + '</td>';
                    content += '<td>' + (item.name || '') + '</td>';
                    content += '<td>' + (item.type_name || '未分类') + '</td>';
                    content += '<td>' + (item.defect_items || '无') + '</td>';
                    content += '</tr>';
                });
                
                content += '</tbody></table></div>';
            } else {
                content = '<div style="padding: 15px; text-align: center; color: #999;">该工艺路线暂无工序信息</div>';
            }
            
            layer.open({
                type: 1,
                title: '工序详情',
                area: ['600px', '400px'],
                content: content,
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        } else {
            layer.msg(res.msg || '获取工序详情失败', {icon: 2});
        }
    }).fail(function() {
        layer.msg('网络请求失败', {icon: 2});
    });
}
</script>
{/block}
