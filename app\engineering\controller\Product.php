<?php
declare (strict_types = 1);
namespace app\engineering\controller;
use app\base\BaseController;
use app\engineering\model\Product as ProductModel;
use app\engineering\validate\ProductValidate;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Product extends BaseController
{
    /**
     * 构造函数
     */
    protected $model;
    public function __construct()
    {
        parent::__construct(); // 调用父类构造函数
        $this->model = new ProductModel();
    }
    
    /**
     * 产品管理首页
     */
    public function index()
    {
        return view(); 
    }
    
    /**
     * 产品列表
     */
    public function datalist()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            if (!empty($param['keywords'])) {
                $where[] = ['p.title|p.material_code|p.specs', 'like', '%' . $param['keywords'] . '%'];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['p.status', '=', $param['status']];
            }
            if (!empty($param['cate_id'])) {
                $cate_id_array = get_cate_son('ProductCate', $param['cate_id']);
                $where[] = ['p.cate_id', 'in', $cate_id_array];
            }
            $where[] = ['p.delete_time', '=', 0];
            $list = $this->model->datalist($where, $param);
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }
    
    /**
    /**
     * 获取客户列表
     */
    public function getCustomerList()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            $where[] = ['status', '=', 1]; // 只获取启用的客户
            
            // 如果有关键词搜索
            if (!empty($param['keywords'])) {
                $where[] = ['code|name', 'like', '%' . $param['keywords'] . '%'];
            }
            
            $list = Db::name('customer')
                ->where($where)
                ->field('id, code, name, is_default')
                ->order('is_default desc, id asc')
                ->select();
            
            // 格式化数据
            $result = [];
            foreach ($list as $item) {
                $result[] = [
                    'id' => $item['id'],
                    'code' => $item['code'],
                    'name' => $item['name'],
                    'title' => $item['name'] . '(' . $item['code'] . ')', // 显示名称
                    'is_default' => $item['is_default']
                ];
            }
            
            return to_assign(0, '获取成功', $result);
        }
        
        return to_assign(1, '请求方式错误');
    }

    /**
     * 新增/编辑产品
     */
    public function add()
    {
        $param = get_params();
        if (request()->isAjax()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(ProductValidate::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $param['update_id'] = $this->uid;
                
                // 确保source_type字段存在
                if (!isset($param['source_type']) || empty($param['source_type'])) {
                    // 如果未设置，查询分类的来源类型设置
                    if (isset($param['cate_id']) && $param['cate_id'] > 0) {
                        $category = Db::name('ProductCate')->where('id', $param['cate_id'])->find();
                        if ($category && isset($category['source_type']) && !empty($category['source_type'])) {
                            $param['source_type'] = $category['source_type'];
                        } else {
                            $param['source_type'] = 1; // 默认自产
                        }
                    } else {
                        $param['source_type'] = 1; // 默认自产
                    }
                }
                
                $this->model->edit($param);
            } else {
                try {
                    validate(ProductValidate::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $category = Db::name('ProductCate')->where('id', $param['cate_id'])->where('delete_time', 0)->find();

                // 根据参数生成物料编号
                // if (empty($param['material_code'])) {
                //     if (isset($param['code_type']) && $param['code_type'] == 'random') {
                //         $param['material_code'] = $this->generateRandomCode();
                //     } else if (isset($param['cate_id']) && $param['cate_id'] > 0) {
                //         // 获取分类信息
                //         if ($category && !empty($category['code'])) {
                //             // 使用分类编码生成物料编号
                //             $productcate = new Productcate();
                //             $param['material_code'] = $productcate->generateProductCode($category['code']);
                            
                //             // 如果分类有来源类型，采用相同的来源类型
                //             if (isset($category['source_type']) && !empty($category['source_type'])) {
                //                 $param['source_type'] = $category['source_type'];
                //             }
                //         } else {
                //             // 使用自增序列生成物料编码
                //             $param['material_code'] = $this->generateSequentialCode('PM');
                //         }
                //     } else {
                //         // 使用自增序列生成物料编码
                //         $param['material_code'] = $this->generateSequentialCode('PM');
                //     }
                // }else{

                //     $productcate = new Productcate();
                //     $param['material_code'] = $productcate->generateProductCode_zd($category['code'],$param['material_code']);
                // }
                
                $param['create_time'] = time();
                $param['admin_id'] = $this->uid;
              
                $this->model->add($param);
            }
            return to_assign();
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            if ($id > 0) {
                $detail = $this->model->getById($id);
                if ($detail['file_ids'] != '') {
                    $file_array = Db::name('File')->where('id', 'in', $detail['file_ids'])->select();
                    $detail['file_array'] = $file_array;
                }
                View::assign('detail', $detail);
                return view('edit');
            }
            return view();
        }
    }

    /**
     * 查看产品详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $detail = $this->model->getById($id);
        if ($detail['cate_id'] > 0) {
            $detail['cate'] = Db::name('ProductCate')->where('id', $detail['cate_id'])->value('title');
        }
        if ($detail['file_ids'] != '') {
            $file_array = Db::name('File')->where('id', 'in', $detail['file_ids'])->select();
            $detail['file_array'] = $file_array;
        }
        View::assign('detail', $detail);
        return view();
    }

    /**
     * 设置产品状态
     */
    public function set()
    {
        $param = get_params();
        $res = $this->model->strict(false)->field('id,status')->update($param);
        if ($res) {
            add_log('set', $param['id'], $param);
            return to_assign();
        } else {
            return to_assign(0, '操作失败');
        }
    }

    /**
     * 删除产品
     */
    public function del()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if (request()->isDelete()) {
            $this->model->delById($id);
            return to_assign();
        } else {
            return to_assign(1, "错误的请求");
        }
    }
    
    /**
     * 生成随机物料编码
     */
    private function generateRandomCode()
    {
        // 生成基于时间戳的唯一编码
        $timestamp = time();
        // 随机部分
        $random = rand(1000, 9999);
        // 组合编码
        $code = 'PM' . date('Ymd', $timestamp) . $random;
        
        // 确保编码唯一
        $exists = Db::name('Product')->where('material_code', $code)->find();
        if ($exists) {
            return $this->generateRandomCode(); // 递归调用直到生成唯一编码
        }
        
        return $code;
    }

    /**
     * 生成自增序列物料编码
     * @param string $prefix 编码前缀
     * @return string 生成的编码
     */
    private function generateSequentialCode($prefix = 'PM')
    {
        // 查找最后一个以该前缀开头的编码
        $lastCode = Db::name('Product')
            ->where('material_code', 'like', $prefix.'%')
            ->where('delete_time', 0)
            ->order('id', 'desc')
            ->value('material_code');
            
        if ($lastCode) {
            // 提取数字部分
            preg_match('/[0-9]+$/', $lastCode, $matches);
            
            if (!empty($matches)) {
                // 获取最后的数字并加1
                $lastNumber = intval($matches[0]);
                $nextNumber = $lastNumber + 1;
                
                // 保持相同的长度，前导零填充
                $numberLength = strlen($matches[0]);
                $newNumber = str_pad($nextNumber, $numberLength, '0', STR_PAD_LEFT);
                
                // 替换原编码中的数字部分
                $newCode = preg_replace('/[0-9]+$/', $newNumber, $lastCode);
                
                // 确保编码唯一
                $exists = Db::name('Product')->where('material_code', $newCode)->where('delete_time', 0)->find();
                if ($exists) {
                    // 如果已存在，递增后再试
                    return $this->generateSequentialCode($prefix);
                }
                
                return $newCode;
            }
        }
        
        // 如果没有找到现有编码或无法提取数字，则创建新的格式
        // 格式：前缀 + 日期 + 5位序列号(00001)
        // 格式：前缀 + 日期 + 5位序列号(00001)
        $date = date('Ymd');
        return $prefix.$date.'00001';
    }
    
    /**
     * 获取仓库列表
     */
    public function getWarehouseList()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            $where[] = ['status', '=', 1]; // 只获取启用的仓库
            
            // 如果有关键词搜索
            if (!empty($param['keywords'])) {
                $where[] = ['code|name', 'like', '%' . $param['keywords'] . '%'];
            }
            
            $list = Db::name('warehouse')
                ->where($where)
                ->field('id, code, name, address, type, is_default')
                ->order('is_default desc, id asc')
                ->select();
            
            // 格式化数据
            $result = [];
            foreach ($list as $item) {
                $result[] = [
                    'id' => $item['id'],
                    'code' => $item['code'],
                    'name' => $item['name'],
                    'title' => $item['name'] . '(' . $item['code'] . ')', // 显示名称
                    'address' => $item['address'],
                    'is_default' => $item['is_default']
                ];
            }
            
            return to_assign(0, '获取成功', $result);
        }
        
        return to_assign(1, '请求方式错误');
    }
}
