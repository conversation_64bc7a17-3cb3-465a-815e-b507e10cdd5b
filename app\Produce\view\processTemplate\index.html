{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">模板中心</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="process_no" placeholder="请输入工艺编号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="process_name" placeholder="请输入工艺名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="请输入编号/工艺名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">重置</button>
        </div>
    </form>
    <table class="layui-hide" id="table_process_template" lay-filter="table_process_template"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>新增</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="import">
        <span>批量导入</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="refresh">
        <span>刷新</span>
    </button>
  </div>
</script>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool;
        
        layui.pageTable = table.render({
            elem: "#table_process_template"
            ,title: "工艺路线模板列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/Produce/processTemplate/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {
                    type: 'checkbox',
                    width: 50
                },{
                    field: 'template_no',
                    title: '工艺编号',
                    align: 'center',
                    width: 180,
                    templet: function(d) {
                        return '<span class="layui-badge layui-bg-blue">' + (d.template_no || '') + '</span>';
                    }
                },{
                    field: 'name',
                    title: '工艺名称',
                    minWidth: 200,
                    templet: '<div><a data-href="/Produce/processTemplate/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'
                },{
                    field: 'step_names',
                    title: '工艺流程',
                    minWidth: 300,
                    templet: function(d) {
                        console.log('工艺流程数据:', d); // 调试用
                        if (d.step_names && d.step_names !== '暂无工艺步骤') {
                            return '<div class="process-flow">' + d.step_names + '</div>';
                        }
                        return '<span class="layui-font-gray">暂无工艺步骤</span>';
                    }
                },{
                    field: 'step_count',
                    title: '步骤数',
                    align: 'center',
                    width: 80,
                    templet: function(d) {
                        var count = d.step_count || 0;
                        var badgeClass = count > 0 ? 'layui-bg-blue' : 'layui-bg-gray';
                        return '<span class="layui-badge ' + badgeClass + '">' + count + '</span>';
                    }
                },{
                    field: 'remark',
                    title: '备注',
                    width: 150,
                    templet: function(d) {
                        if (d.remark && d.remark.length > 20) {
                            return '<span title="' + d.remark + '">' + d.remark.substring(0, 20) + '...</span>';
                        }
                        return d.remark || '';
                    }
                },{
                    field: 'create_time_format',
                    title: '创建时间',
                    align: 'center',
                    width: 160
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 200,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">编辑</span>';
                        var btn1='<span class="layui-btn layui-btn-xs" lay-event="copy">复制</span>';
                        var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
                        return html+btn0+btn1+btn2+'</div>';
                    }                        
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_process_template)', function(obj){
            if (obj.event === 'add'){
                tool.side("/Produce/processTemplate/add");
                return;
            }
            if (obj.event === 'import'){
                layer.msg('批量导入功能开发中...');
                return;
            }
            if (obj.event === 'refresh'){
                layui.pageTable.reload();
                return;
            }
        });    
            
        table.on('tool(table_process_template)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/Produce/processTemplate/add?id="+data.id);
                return;
            }
            if (obj.event === 'copy') {
                layer.confirm('确定要复制该工艺路线吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/Produce/processTemplate/copy", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'del') {
                layer.confirm('确定要删除该工艺路线吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.del();
                        }
                    }
                    tool.post("/Produce/processTemplate/delete", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
        });
    }
</script>
{/block}

{block name="style"}
<style>
.process-flow {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}
</style>
{/block}