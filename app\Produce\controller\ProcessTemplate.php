<?php
declare (strict_types = 1);

namespace app\Produce\controller;

use app\base\BaseController;
use app\Produce\model\ProcessTemplateModel;
use think\facade\View;
use think\facade\Db;

class ProcessTemplate extends BaseController
{
    /**
     * 工艺路线列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $page = (int)($param['page'] ?? 1);
            $limit = (int)($param['limit'] ?? 15);
            
            // 获取工艺路线列表
            $model = new ProcessTemplateModel();
            $result = $model->getList($param, $page, $limit);
            
            // 返回数据
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'count' => $result['count'],
                'data' => $result['data']
            ]);
        }
        
        // 加载视图
        return View::fetch('process_template/index');
    }
    
    /**
     * 添加/编辑工艺路线页面
     */
    public function add()
    {
        $param = get_params();
        $id = $param['id'] ?? 0;
        $info = ['id' => 0, 'name' => '', 'remark' => '', 'steps' => []];
        $ztype = 0;
        
        // 如果是编辑操作，获取工艺路线信息
        if ($id > 0) {
            $model = new ProcessTemplateModel();
            $info = $model->find($id);
            $ztype = 1;
            if (!$info) {
                return $this->error('工艺路线不存在');
            }
            
            // 解析工艺步骤
            if (!empty($info['steps'])) {
                $stepsData = json_decode($info['steps'], true) ?: [];
                $info['steps_data'] = $stepsData;
                $info['steps_json'] = json_encode($stepsData);
            } else {
                $info['steps_data'] = [];
                $info['steps_json'] = '[]';
            }
        }
        
        // 获取工序列表 - 使用现有的工序表
        $processList = Db::name('produce_process')
            ->field('id, name, code')
            ->order('id desc')
            ->select()
            ->toArray();
        
        // 分配变量到模板
        View::assign([
            'info' => $info,
            'processList' => $processList,
            'ztype' => $ztype
        ]);
        
        // 加载视图
        return View::fetch('process_template/add');
    }
    
    /**
     * 保存工艺路线数据
     */
    public function save()
    {
        // 获取表单数据
        $param = get_params();
        
        // 使用验证器验证数据
        $validate = new \app\Produce\validate\ProcessTemplateValidate();
        if (!$validate->check($param)) {
            return json(['code' => 1, 'msg' => $validate->getError()]);
        }
        
        // 验证工艺步骤数据
        if (empty($param['steps']) || !is_array($param['steps'])) {
            return json(['code' => 1, 'msg' => '请至少添加一个工艺步骤']);
        }
        
        // 使用验证器验证步骤数据
        $stepValidation = $validate->checkSteps($param['steps']);
        if ($stepValidation !== true) {
            return json(['code' => 1, 'msg' => $stepValidation]);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $model = new ProcessTemplateModel();
            
            // 处理工艺路线数据
            $data = [
                'name' => $param['name'],
                'remark' => $param['remark'] ?? '',
                'steps' => json_encode($param['steps']),
                'update_time' => time()
            ];
            
            if(isset($param['id']) && $param['id'] > 0) {
                // 编辑工艺路线
                $templateId = $param['id'];
                $model->where('id', $templateId)->update($data);
            } else {
                // 新增工艺路线
                $data['create_time'] = time();
                $data['template_no'] = $this->generateTemplateNo();
                $templateId = $model->insertGetId($data);
            }
            
            // 提交事务
            Db::commit();
            
            return json(['code' => 0, 'msg' => '保存成功']);
        } catch(\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 查看工艺路线详情页面
     */
    public function view()
    {
        // 获取工艺路线ID
        $id = request()->param('id/d', 0);
        
        // 获取工艺路线信息
        $model = new ProcessTemplateModel();
        $info = $model->find($id);
        
        if (!$info) {
            return $this->error('工艺路线不存在');
        }
        
        // 解析工艺步骤
        if (!empty($info['steps'])) {
            $info['steps'] = json_decode($info['steps'], true) ?: [];
        }
        
        // 分配变量到模板
        View::assign([
            'info' => $info
        ]);
        
        // 加载视图
        return View::fetch('process_template/view');
    }
    
    /**
     * 复制工艺路线
     */
    public function copy()
    {
        // 判断是否为POST请求
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        // 获取工艺路线ID
        $id = request()->param('id/d');
        
        // 参数验证
        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取原工艺路线信息
        $model = new ProcessTemplateModel();
        $original = $model->find($id);
        
        if (!$original) {
            return json(['code' => 1, 'msg' => '工艺路线不存在']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 创建副本数据
            $copyData = [
                'template_no' => $this->generateTemplateNo(),
                'name' => $original['name'] . '_副本',
                'remark' => $original['remark'],
                'steps' => $original['steps'],
                'create_time' => time(),
                'update_time' => time()
            ];
            
            // 插入副本
            $copyId = $model->insertGetId($copyData);
            
            // 提交事务
            Db::commit();
            
            return json(['code' => 0, 'msg' => '复制成功', 'data' => ['id' => $copyId]]);
        } catch(\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '复制失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除工艺路线
     */
    public function delete()
    {
        // 判断是否为POST请求
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        // 获取工艺路线ID
        $id = request()->param('id/d');
        
        // 参数验证
        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 检查是否被使用
        $usedCount = Db::name('produce_order')
            ->where('process_template_id', $id)
            ->count();
        
        if ($usedCount > 0) {
            return json(['code' => 1, 'msg' => '该工艺路线已被使用，无法删除']);
        }
        
        // 删除工艺路线
        $model = new ProcessTemplateModel();
        $result = $model->where('id', $id)->delete();
        
        if ($result) {
            return json(['code' => 0, 'msg' => '删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '删除失败']);
        }
    }
    
    /**
     * 获取工序列表（用于选择）
     */
    public function getProcessList()
    {
        $keyword = request()->param('keyword', '');
        $page = request()->param('page/d', 1);
        $limit = request()->param('limit/d', 100); // 增加限制数量，获取更多工序
        
        $where = [];
        if ($keyword) {
            $where[] = ['name', 'like', "%{$keyword}%"];
        }
        
        $list = Db::name('produce_process')
            ->where($where)
            ->field('id, name, code')
            ->order('id desc')
            ->limit($limit)
            ->select()
            ->toArray();
            
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $list
        ]);
    }
    
    /**
     * 批量导入工艺路线
     */
    public function import()
    {
        // TODO: 实现批量导入功能
        return json(['code' => 1, 'msg' => '导入功能尚未实现']);
    }
    
    /**
     * 生成工艺路线模板编号
     * 格式：GYLX年月日+5位递增数字
     * @return string
     */
    private function generateTemplateNo()
    {
        $date = date('Ymd');
        $prefix = 'GYLX' . $date;
        
        // 查询当天最大编号
        $maxNo = Db::name('process_template')
            ->where('template_no', 'like', $prefix . '%')
            ->order('template_no', 'desc')
            ->value('template_no');
        
        if ($maxNo) {
            // 提取后5位数字并加1
            $serialNumber = intval(substr($maxNo, -5)) + 1;
        } else {
            // 当天第一个编号
            $serialNumber = 1;
        }
        
        // 使用 sprintf 格式化为5位数字
        $serialNumberStr = sprintf('%05d', $serialNumber);
        
        return $prefix . $serialNumberStr;
    }
}