<?php
declare (strict_types = 1);
namespace app\Produce\controller;
use app\base\BaseController;
use think\facade\Db;
use think\facade\View;

class ProcessTemplate extends BaseController
{
    /**
     * 工艺路线模板首页
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['process_no'])) {
                $where[] = ['template_no', 'like', '%' . $param['process_no'] . '%'];
            }
            if (!empty($param['process_name'])) {
                $where[] = ['name', 'like', '%' . $param['process_name'] . '%'];
            }
            if (!empty($param['keywords'])) {
                $where[] = ['name|template_no', 'like', '%' . $param['keywords'] . '%'];
            }
            
            // 查询工艺路线模板
            $list = Db::name('process_template')
                ->where($where)
                ->field('id, template_no, name, remark, create_time, steps')
                ->order('id desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 20,
                    'page' => $param['page'] ?? 1
                ]);
            
            // 处理数据
            foreach ($list as &$item) {
                $item['create_time_format'] = date('Y-m-d H:i:s', $item['create_time']);
                
                // 解析工艺步骤
                if (!empty($item['steps'])) {
                    $steps = json_decode($item['steps'], true);
                    
                    if (is_array($steps) && !empty($steps)) {
                        $stepNames = [];
                        foreach ($steps as $step) {
                            if (isset($step['name']) && !empty($step['name'])) {
                                $stepNames[] = $step['name'];
                            }
                        }
                        
                        if (!empty($stepNames)) {
                            $item['step_names'] = implode(' → ', $stepNames);
                            $item['step_count'] = count($stepNames);
                        } else {
                            $item['step_names'] = '暂无工艺步骤';
                            $item['step_count'] = 0;
                        }
                    } else {
                        $item['step_names'] = '暂无工艺步骤';
                        $item['step_count'] = 0;
                    }
                } else {
                    $item['step_names'] = '暂无工艺步骤';
                    $item['step_count'] = 0;
                }
            }
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }
    
    /**
     * 添加/编辑工艺路线模板
     */
    public function add()
    {
        $param = get_params();
        if (request()->isAjax()) {
            // 这里处理保存逻辑
            return to_assign(0, '保存成功');
        } else {
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $info = [];
            if ($id > 0) {
                $info = Db::name('process_template')->where('id', $id)->find();
            }
            View::assign('info', $info);
            return view();
        }
    }
    
    /**
     * 保存工艺路线模板
     */
    public function save()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
            $data = [
                'name' => $param['name'] ?? '',
                'remark' => $param['remark'] ?? '',
                'steps' => json_encode($param['steps'] ?? [], JSON_UNESCAPED_UNICODE),
                'update_time' => time()
            ];
            
            if (isset($param['id']) && $param['id'] > 0) {
                // 更新
                Db::name('process_template')->where('id', $param['id'])->update($data);
            } else {
                // 新增
                $data['template_no'] = 'PT' . date('YmdHis') . rand(1000, 9999);
                $data['create_time'] = time();
                Db::name('process_template')->insert($data);
            }
            
            return to_assign(0, '保存成功');
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 复制工艺路线模板
     */
    public function copy()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'] ?? 0;
            
            if ($id <= 0) {
                return to_assign(1, '参数错误');
            }
            
            $original = Db::name('process_template')->where('id', $id)->find();
            if (!$original) {
                return to_assign(1, '原模板不存在');
            }
            
            $data = [
                'template_no' => 'PT' . date('YmdHis') . rand(1000, 9999),
                'name' => $original['name'] . '_副本',
                'remark' => $original['remark'],
                'steps' => $original['steps'],
                'create_time' => time()
            ];
            
            Db::name('process_template')->insert($data);
            
            return to_assign(0, '复制成功');
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 删除工艺路线模板
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'] ?? 0;
            
            if ($id <= 0) {
                return to_assign(1, '参数错误');
            }
            
            Db::name('process_template')->where('id', $id)->delete();
            
            return to_assign(0, '删除成功');
        }
        
        return to_assign(1, '请求方式错误');
    }
    
 
    
    /**
     * 工艺路线选择页面
     */
    public function select()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 如果有关键词搜索
            if (!empty($param['keywords'])) {
                $where[] = ['name|template_no', 'like', '%' . $param['keywords'] . '%'];
            }
            
            // 查询工艺路线模板
            $list = Db::name('process_template')
                ->where($where)
                ->field('id, template_no as code, name, remark, create_time')
                ->order('id desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 15,
                    'page' => $param['page'] ?? 1
                ]);
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }
    
    /**
     * 获取工艺路线详情（包含工序列表）
     */
    public function getProcessDetail()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $template_id = $param['template_id'] ?? 0;
            
            if ($template_id <= 0) {
                return to_assign(1, '参数错误');
            }
            
            // 获取工艺路线基本信息
            $template = Db::name('process_template')
                ->where('id', $template_id)
                ->find();
                
            if (!$template) {
                return to_assign(1, '工艺路线不存在');
            }
            
            // 从steps字段解析工序列表
            $processes = [];
            if (!empty($template['steps'])) {
                $steps = json_decode($template['steps'], true);
                if (is_array($steps)) {
                    foreach ($steps as $index => $step) {
                        $processes[] = [
                            'id' => $index + 1,
                            'name' => $step['name'] ?? '',
                            'type_name' => $step['type'] ?? '数据记录',
                            'defect_items' => $step['description'] ?? '',
                            'processing_type' => $step['processing_type'] ?? '自制',
                            'inspection_method' => $step['inspection_method'] ?? '免检',
                            'completion_time' => $step['completion_time'] ?? 0,
                            'time_unit' => $step['time_unit'] ?? '天'
                        ];
                    }
                }
            }
            
            $result = [
                'template' => $template,
                'processes' => $processes
            ];
            
            return to_assign(0, '获取成功', $result);
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 查看工艺路线详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        if ($id <= 0) {
            return redirect('/Produce/processTemplate/index');
        }
        
        $info = Db::name('process_template')->where('id', $id)->find();
        if (!$info) {
            return redirect('/Produce/processTemplate/index');
        }
        
        // 解析工艺步骤
        if (!empty($info['steps'])) {
            $steps = json_decode($info['steps'], true);
            $info['step_list'] = is_array($steps) ? $steps : [];
        } else {
            $info['step_list'] = [];
        }
        
        View::assign('info', $info);
        return view();
    }
    
 
}
