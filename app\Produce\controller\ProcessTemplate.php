<?php
declare (strict_types = 1);
namespace app\Produce\controller;
use app\base\BaseController;
use think\facade\Db;
use think\facade\View;

class ProcessTemplate extends BaseController
{
    /**
     * 工艺路线模板首页
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['process_no'])) {
                $where[] = ['template_no', 'like', '%' . $param['process_no'] . '%'];
            }
            if (!empty($param['process_name'])) {
                $where[] = ['name', 'like', '%' . $param['process_name'] . '%'];
            }
            if (!empty($param['keywords'])) {
                $where[] = ['name|template_no', 'like', '%' . $param['keywords'] . '%'];
            }
            
            // 查询工艺路线模板
            $list = Db::name('process_template')
                ->where($where)
                ->field('id, template_no, name, remark, create_time, steps')
                ->order('id desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 20,
                    'page' => $param['page'] ?? 1
                ]);
            
            // 处理数据
            foreach ($list as &$item) {
                $item['create_time_format'] = date('Y-m-d H:i:s', $item['create_time']);

                // 解析工艺步骤
                if (!empty($item['steps'])) {
                    $steps = $this->parseStepsJson($item['steps']);

                    // 调试信息
                    if ($item['id'] == 7) {
                        error_log("调试 - 模板ID 7:");
                        error_log("原始steps: " . substr($item['steps'], 0, 200));
                        error_log("解析结果: " . json_encode($steps, JSON_UNESCAPED_UNICODE));
                    }

                    if (is_array($steps) && !empty($steps)) {
                        $stepNames = [];
                        foreach ($steps as $step) {
                            if (isset($step['name']) && !empty($step['name'])) {
                                $stepNames[] = $step['name'];
                            }
                        }

                        if (!empty($stepNames)) {
                            $item['step_names'] = implode(' → ', $stepNames);
                            $item['step_count'] = count($stepNames);
                        } else {
                            $item['step_names'] = '暂无工艺步骤';
                            $item['step_count'] = 0;
                        }
                    } else {
                        $item['step_names'] = '暂无工艺步骤';
                        $item['step_count'] = 0;
                    }
                } else {
                    $item['step_names'] = '暂无工艺步骤';
                    $item['step_count'] = 0;
                }
            }
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }
    
    /**
     * 添加/编辑工艺路线模板
     */
    public function add()
    {
        $param = get_params();
        if (request()->isAjax()) {
            // 这里处理保存逻辑
            return to_assign(0, '保存成功');
        } else {
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $info = [];
            if ($id > 0) {
                $info = Db::name('process_template')->where('id', $id)->find();
            }
            View::assign('info', $info);
            return view();
        }
    }
    
    /**
     * 保存工艺路线模板
     */
    public function save()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
            $data = [
                'name' => $param['name'] ?? '',
                'remark' => $param['remark'] ?? '',
                'steps' => json_encode($param['steps'] ?? [], JSON_UNESCAPED_UNICODE),
                'update_time' => time()
            ];
            
            if (isset($param['id']) && $param['id'] > 0) {
                // 更新
                Db::name('process_template')->where('id', $param['id'])->update($data);
            } else {
                // 新增
                $data['template_no'] = 'PT' . date('YmdHis') . rand(1000, 9999);
                $data['create_time'] = time();
                Db::name('process_template')->insert($data);
            }
            
            return to_assign(0, '保存成功');
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 复制工艺路线模板
     */
    public function copy()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'] ?? 0;
            
            if ($id <= 0) {
                return to_assign(1, '参数错误');
            }
            
            $original = Db::name('process_template')->where('id', $id)->find();
            if (!$original) {
                return to_assign(1, '原模板不存在');
            }
            
            $data = [
                'template_no' => 'PT' . date('YmdHis') . rand(1000, 9999),
                'name' => $original['name'] . '_副本',
                'remark' => $original['remark'],
                'steps' => $original['steps'],
                'create_time' => time()
            ];
            
            Db::name('process_template')->insert($data);
            
            return to_assign(0, '复制成功');
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 删除工艺路线模板
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'] ?? 0;
            
            if ($id <= 0) {
                return to_assign(1, '参数错误');
            }
            
            Db::name('process_template')->where('id', $id)->delete();
            
            return to_assign(0, '删除成功');
        }
        
        return to_assign(1, '请求方式错误');
    }
    
 
    
    /**
     * 工艺路线选择页面
     */
    public function select()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 如果有关键词搜索
            if (!empty($param['keywords'])) {
                $where[] = ['name|template_no', 'like', '%' . $param['keywords'] . '%'];
            }
            
            // 查询工艺路线模板
            $list = Db::name('process_template')
                ->where($where)
                ->field('id, template_no as code, name, remark, create_time')
                ->order('id desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 15,
                    'page' => $param['page'] ?? 1
                ]);
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }
    
    /**
     * 获取工艺路线详情（包含工序列表）
     */
    public function getProcessDetail()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $template_id = $param['template_id'] ?? 0;
            
            if ($template_id <= 0) {
                return to_assign(1, '参数错误');
            }
            
            // 获取工艺路线基本信息
            $template = Db::name('process_template')
                ->where('id', $template_id)
                ->find();
                
            if (!$template) {
                return to_assign(1, '工艺路线不存在');
            }
            
            // 从steps字段解析工序列表
            $processes = [];
            $steps = $this->parseStepsJson($template['steps'] ?? '');
            if (is_array($steps)) {
                foreach ($steps as $index => $step) {
                    $processes[] = [
                        'id' => $index + 1,
                        'name' => $step['name'] ?? '',
                        'type_name' => $step['type'] ?? '数据记录',
                        'defect_items' => $step['description'] ?? '',
                        'processing_type' => $step['processing_type'] ?? '自制',
                        'inspection_method' => $step['inspection_method'] ?? '免检',
                        'completion_time' => $step['completion_time'] ?? 0,
                        'time_unit' => $step['time_unit'] ?? '天'
                    ];
                }
            }
            
            $result = [
                'template' => $template,
                'processes' => $processes
            ];
            
            return to_assign(0, '获取成功', $result);
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 查看工艺路线详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        if ($id <= 0) {
            return redirect('/Produce/processTemplate/index');
        }
        
        $info = Db::name('process_template')->where('id', $id)->find();
        if (!$info) {
            return redirect('/Produce/processTemplate/index');
        }
        
        // 解析工艺步骤
        $info['step_list'] = $this->parseStepsJson($info['steps'] ?? '');
        
        View::assign('info', $info);
        return view();
    }

    /**
     * 解析工艺步骤JSON数据
     * 处理Unicode转义字符问题
     */
    private function parseStepsJson($stepsJson)
    {
        if (empty($stepsJson)) {
            return [];
        }

        $hasUnicodeEscape = strpos($stepsJson, '\\u') !== false;

        // 方法1：直接解析（处理正常的JSON）
        $steps = json_decode($stepsJson, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($steps)) {
            if ($hasUnicodeEscape) {
                error_log("方法1成功解析包含Unicode转义的JSON");
            }
            return $steps;
        }

        if ($hasUnicodeEscape) {
            error_log("方法1失败，尝试方法2处理Unicode转义字符");

            // 方法2：处理Unicode转义字符
            $fixedJson = preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function($matches) {
                return json_decode('"\u' . $matches[1] . '"');
            }, $stepsJson);

            error_log("修复后的JSON: " . substr($fixedJson, 0, 200));

            $steps = json_decode($fixedJson, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($steps)) {
                error_log("方法2成功解析");
                return $steps;
            } else {
                error_log("方法2失败: " . json_last_error_msg());
            }
        }

        // 方法3：处理双重转义
        $unescapedJson = stripslashes($stepsJson);
        $steps = json_decode($unescapedJson, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($steps)) {
            error_log("方法3成功解析");
            return $steps;
        }

        // 如果都失败了，记录错误并返回空数组
        error_log("所有方法都失败了: " . json_last_error_msg() . ", 原始数据: " . substr($stepsJson, 0, 200));

        return [];
    }

    /**
     * 测试JSON解析功能
     * 临时调试接口
     */
    public function testJsonParse()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'] ?? 0;

            if ($id <= 0) {
                return to_assign(1, '请提供模板ID');
            }

            $template = Db::name('process_template')
                ->where('id', $id)
                ->find();

            if (!$template) {
                return to_assign(1, '模板不存在');
            }

            $originalSteps = $template['steps'];
            $parsedSteps = $this->parseStepsJson($originalSteps);

            $result = [
                'template_id' => $id,
                'template_name' => $template['name'],
                'original_steps' => $originalSteps,
                'original_length' => strlen($originalSteps),
                'has_unicode_escape' => strpos($originalSteps, '\\u') !== false,
                'parsed_steps' => $parsedSteps,
                'parsed_count' => count($parsedSteps),
                'step_names' => []
            ];

            // 提取步骤名称
            foreach ($parsedSteps as $step) {
                if (isset($step['name'])) {
                    $result['step_names'][] = $step['name'];
                }
            }

            return to_assign(0, '解析成功', $result);
        }

        return to_assign(1, '请求方式错误');
    }
}
